import { useCallback, useEffect, useRef, useState } from 'react';

import Head from 'next/head';
import { useDispatch, useSelector } from 'react-redux';

// Additional MUI imports for Dialog and Select
import { Dialog, DialogActions, DialogContent, DialogTitle } from '@mui/material';

// MUI Components
import {
    Alert,
    Avatar,
    Box,
    Button,
    Card,
    CardContent,
    CardHeader,
    Chip,
    CircularProgress,
    FormControlLabel,
    Grid,
    IconButton,
    List,
    ListItem,
    Paper,
    Snackbar,
    Switch,
    TextField,
    Tooltip,
    Typography,
    useTheme
} from '@mui/material';

// MUI Icons - Sleek Outlined Versions
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import ClearOutlinedIcon from '@mui/icons-material/ClearOutlined';
import DesktopWindowsOutlinedIcon from '@mui/icons-material/DesktopWindowsOutlined';
import MicNoneOutlinedIcon from '@mui/icons-material/MicNoneOutlined';
import MicOffOutlinedIcon from '@mui/icons-material/MicOffOutlined';
import PersonOutlineIcon from '@mui/icons-material/PersonOutline';
import PictureInPictureAltOutlinedIcon from '@mui/icons-material/PictureInPictureAltOutlined';
import QuizOutlinedIcon from '@mui/icons-material/QuizOutlined';
import SendOutlinedIcon from '@mui/icons-material/SendOutlined';
import SettingsOutlinedIcon from '@mui/icons-material/SettingsOutlined';
import SmartToyOutlinedIcon from '@mui/icons-material/SmartToyOutlined';
import StopOutlinedIcon from '@mui/icons-material/StopOutlined';
import UploadFileOutlinedIcon from '@mui/icons-material/UploadFileOutlined';


// Third-party Libraries
import hljs from 'highlight.js';
import 'highlight.js/styles/atom-one-dark.css';
import throttle from 'lodash.throttle';
import OpenAI from 'openai';
import ReactMarkdown from 'react-markdown';
import ScrollToBottom from 'react-scroll-to-bottom';

// Speech Services

// Local Imports

import SettingsDialog from '../components/SettingsDialog';
import { setAIResponse } from '../redux/aiResponseSlice';
import { addToHistory } from '../redux/historySlice';
import { clearTranscription, setTranscription } from '../redux/transcriptionSlice';
import { getConfig } from '../utils/config';
import { createSpeechService } from '../utils/speechServices';



function debounce(func, timeout = 100) {
  let timer;
  return (...args) => {
    clearTimeout(timer);
    timer = setTimeout(() => {
      func.apply(this, args);
    }, timeout);
  };
}


export default function InterviewPage() {
  const dispatch = useDispatch();
  const transcriptionFromStore = useSelector(state => state.transcription);
  const aiResponseFromStore = useSelector(state => state.aiResponse);
  const history = useSelector(state => state.history);
  const theme = useTheme();

  const [appConfig, setAppConfig] = useState(getConfig());

  const [systemRecognizer, setSystemRecognizer] = useState(null);
  const [micRecognizer, setMicRecognizer] = useState(null);
  const [systemSpeechService, setSystemSpeechService] = useState(null);
  const [micSpeechService, setMicSpeechService] = useState(null);
  const [activeSpeechService, setActiveSpeechService] = useState(null); // Track which service is active
  const [systemAutoMode, setSystemAutoMode] = useState(appConfig.systemAutoMode !== undefined ? appConfig.systemAutoMode : true);
  const [openAI, setOpenAI] = useState(null);
  const [settingsOpen, setSettingsOpen] = useState(false);

  const [isMicrophoneActive, setIsMicrophoneActive] = useState(false);
  const [isSystemAudioActive, setIsSystemAudioActive] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('info');

  const [isManualMode, setIsManualMode] = useState(appConfig.isManualMode !== undefined ? appConfig.isManualMode : false);
  const [micTranscription, setMicTranscription] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [isAILoading, setIsAILoading] = useState(true);

  // Resume and Job Description state
  const [resumeText, setResumeText] = useState('');
  const [jobDescriptionText, setJobDescriptionText] = useState('');
  const [showDocumentUpload, setShowDocumentUpload] = useState(false);
  const [availableResumes, setAvailableResumes] = useState([]);
  const [selectedResumeFile, setSelectedResumeFile] = useState('');
  const [showPromptPreview, setShowPromptPreview] = useState(false);
  const [autoScroll, setAutoScroll] = useState(true);
  const [aiResponseSortOrder, setAiResponseSortOrder] = useState('newestAtTop');
  const [isPipWindowActive, setIsPipWindowActive] = useState(false);

  const pipWindowRef = useRef(null);
  const documentPipWindowRef = useRef(null);
  const documentPipIframeRef = useRef(null);
  const systemInterimTranscription = useRef('');
  const micInterimTranscription = useRef('');
  const silenceTimer = useRef(null);
  const finalTranscript = useRef({ system: '', microphone: '' });
  const isManualModeRef = useRef(isManualMode);
  const systemAutoModeRef = useRef(systemAutoMode);
  const throttledDispatchSetAIResponseRef = useRef(null);

  const showSnackbar = useCallback((message, severity = 'info') => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  }, []);

  const handleSettingsSaved = () => {
    const newConfig = getConfig();
    setAppConfig(newConfig);
    setIsAILoading(true);
    setSystemAutoMode(newConfig.systemAutoMode !== undefined ? newConfig.systemAutoMode : true);
    setIsManualMode(newConfig.isManualMode !== undefined ? newConfig.isManualMode : false);
  };

  // File upload handlers with PDF parsing
  const handleFileUpload = async (file, type) => {
    if (!file) return;

    try {
      let text = '';

      if (file.type === 'application/pdf') {
        // Handle PDF files using pdfjs-dist
        try {
          const pdfjsLib = await import('pdfjs-dist');

          // Set worker source with fallback - use local worker
          if (typeof window !== 'undefined') {
            // Try to use a more reliable worker source
            pdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@${pdfjsLib.version}/build/pdf.worker.min.js`;
          }

          const arrayBuffer = await file.arrayBuffer();
          console.log('Uploaded PDF file size:', arrayBuffer.byteLength, 'bytes');

          const pdf = await pdfjsLib.getDocument({
            data: arrayBuffer,
            verbosity: 0 // Reduce console noise
          }).promise;

          console.log('Uploaded PDF loaded successfully, pages:', pdf.numPages);

          let fullText = '';
          for (let i = 1; i <= pdf.numPages; i++) {
            const page = await pdf.getPage(i);
            const textContent = await page.getTextContent();
            const pageText = textContent.items.map(item => item.str).join(' ');
            fullText += pageText + '\n';
          }
          text = fullText.trim();

          if (!text || text.length < 50) {
            throw new Error('PDF appears to be empty or contains mostly images');
          }

        } catch (pdfError) {
          console.error('PDF parsing error:', pdfError);
          showSnackbar(`Error parsing PDF: ${pdfError.message}. Try using a text file instead.`, 'error');
          return;
        }
      } else {
        // Handle text files
        text = await file.text();
      }

      if (type === 'resume') {
        setResumeText(text);
        showSnackbar(`Resume uploaded successfully (${Math.round(text.length / 1000)}k characters)`, 'success');
      }
    } catch (error) {
      console.error('Error reading file:', error);
      showSnackbar(`Error reading ${type}: ${error.message}`, 'error');
    }
  };

  const handleResumeUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      // First, parse the file content for immediate use
      await handleFileUpload(file, 'resume');

      // Then, upload to server for persistent storage
      const formData = new FormData();
      formData.append('resume', file);

      const response = await fetch('/api/upload-resume', {
        method: 'POST',
        body: formData,
      });

      if (response.ok) {
        const result = await response.json();
        console.log('File uploaded to server:', result.filePath);
        showSnackbar(`Resume uploaded and saved to ${result.fileName}`, 'success');

        // Refresh available resumes list
        await loadAvailableResumes();
      } else {
        console.error('Upload failed:', response.statusText);
        showSnackbar('Resume parsed but not saved to server', 'warning');
      }
    } catch (error) {
      console.error('Upload error:', error);
      // File parsing still worked, just server upload failed
      showSnackbar('Resume loaded but not saved to server', 'warning');
    }
  };

  // Load available resumes from the public/resumes folder
  const loadAvailableResumes = async () => {
    try {
      // Try to load common resume files
      const commonResumeFiles = [
        'my_resume.pdf',
        'my_resume.txt',
        'resume.pdf',
        'resume.txt',
        'sample_resume.txt'
      ];

      const availableFiles = [];

      for (const fileName of commonResumeFiles) {
        try {
          const response = await fetch(`/resumes/${fileName}`, { method: 'HEAD' });
          if (response.ok) {
            availableFiles.push({
              name: fileName,
              path: `/resumes/${fileName}`
            });
          }
        } catch (e) {
          // File doesn't exist, continue
        }
      }

      setAvailableResumes(availableFiles);
      console.log('Available resumes:', availableFiles);
    } catch (error) {
      console.error('Error loading resumes:', error);
    }
  };

  // Load resume from folder with proper PDF parsing
  const loadResumeFromFolder = async (resumePath) => {
    try {
      const response = await fetch(resumePath);
      if (!response.ok) {
        showSnackbar('Error loading resume file', 'error');
        return;
      }

      let text = '';
      const fileName = resumePath.split('/').pop().toLowerCase();

      if (fileName.endsWith('.pdf')) {
        // Handle PDF files using pdfjs-dist
        try {
          const pdfjsLib = await import('pdfjs-dist');

          // Set worker source with fallback - use local worker
          if (typeof window !== 'undefined') {
            // Try to use a more reliable worker source
            pdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@${pdfjsLib.version}/build/pdf.worker.min.js`;
          }

          const arrayBuffer = await response.arrayBuffer();
          console.log('PDF file size:', arrayBuffer.byteLength, 'bytes');

          const pdf = await pdfjsLib.getDocument({
            data: arrayBuffer,
            verbosity: 0 // Reduce console noise
          }).promise;

          console.log('PDF loaded successfully, pages:', pdf.numPages);

          let fullText = '';
          for (let i = 1; i <= pdf.numPages; i++) {
            const page = await pdf.getPage(i);
            const textContent = await page.getTextContent();
            const pageText = textContent.items.map(item => item.str).join(' ');
            fullText += pageText + '\n';
          }
          text = fullText.trim();

          if (!text || text.length < 50) {
            throw new Error('PDF appears to be empty or contains mostly images');
          }

        } catch (pdfError) {
          console.error('PDF parsing error:', pdfError);
          showSnackbar(`Error parsing PDF: ${pdfError.message}. Try using a text file instead or copy-paste the content manually.`, 'error');
          return;
        }
      } else {
        // Handle text files
        text = await response.text();
      }

      setResumeText(text);
      setSelectedResumeFile(resumePath);

      // Debug logging to help verify PDF parsing
      console.log('Resume loaded from:', resumePath);
      console.log('Resume text length:', text.length);
      console.log('First 200 characters:', text.substring(0, 200));

      showSnackbar(`Resume loaded successfully (${Math.round(text.length / 1000)}k characters)`, 'success');

    } catch (error) {
      console.error('Error loading resume:', error);
      showSnackbar('Error loading resume file', 'error');
    }
  };

  const clearDocuments = () => {
    setResumeText('');
    setJobDescriptionText('');
    setSelectedResumeFile('');
    showSnackbar('Documents cleared', 'info');
  };

  // Load available resumes on component mount
  useEffect(() => {
    loadAvailableResumes();
  }, []);

  // Generate enhanced system prompt with resume and job description context
  const getEnhancedSystemPrompt = (basePrompt) => {
    let enhancedPrompt = basePrompt;

    if (resumeText || jobDescriptionText) {
      enhancedPrompt += '\n\n--- CONTEXT DOCUMENTS ---\n';

      if (resumeText) {
        enhancedPrompt += `\nCANDIDATE RESUME:\n${resumeText}\n`;
      }

      if (jobDescriptionText) {
        enhancedPrompt += `\nJOB DESCRIPTION:\n${jobDescriptionText}\n`;
      }

      enhancedPrompt += '\n--- INSTRUCTIONS ---\n';
      enhancedPrompt += 'Use the above context to provide personalized interview coaching. ';
      enhancedPrompt += 'Reference specific experiences from the resume and align answers with the job requirements. ';
      enhancedPrompt += 'Suggest how to highlight relevant skills and experiences that match the job description. ';
      enhancedPrompt += 'Provide specific examples and metrics from the resume when applicable.';
    }

    return enhancedPrompt;
  };

  useEffect(() => {
    const currentConfig = appConfig;
    const initializeAI = async () => {
      try {
        if (currentConfig.aiModel.startsWith('gemini')) {
          if (!currentConfig.geminiKey) {
            showSnackbar('Gemini API key required. Please set it in Settings.', 'error');
            setOpenAI(null);
            return;
          }
          // Use dynamic import to avoid build issues
          const { GoogleGenAI } = await import('@google/genai');
          const genAI = new GoogleGenAI({ apiKey: currentConfig.geminiKey });
          setOpenAI(genAI);
        } else {
          if (!currentConfig.openaiKey) {
            showSnackbar('OpenAI API key required. Please set it in Settings.', 'error');
            setOpenAI(null);
            return;
          }
          const openaiClient = new OpenAI({
            apiKey: currentConfig.openaiKey,
            dangerouslyAllowBrowser: true
          });
          setOpenAI(openaiClient);
        }
      } catch (error) {
        console.error('Error initializing AI client:', error);
        showSnackbar('Error initializing AI client: ' + error.message, 'error');
        setOpenAI(null);
      } finally {
        setIsAILoading(false);
      }
    };
    if (isAILoading) initializeAI();
  }, [appConfig, isAILoading, showSnackbar]);

  useEffect(() => { isManualModeRef.current = isManualMode; }, [isManualMode]);
  useEffect(() => { systemAutoModeRef.current = systemAutoMode; }, [systemAutoMode]);

  useEffect(() => {
    throttledDispatchSetAIResponseRef.current = throttle((payload) => {
      dispatch(setAIResponse(payload));
    }, 250, { leading: true, trailing: true });

    return () => {
      if (throttledDispatchSetAIResponseRef.current && typeof throttledDispatchSetAIResponseRef.current.cancel === 'function') {
        throttledDispatchSetAIResponseRef.current.cancel();
      }
    };
  }, [dispatch]);

  const handleSnackbarClose = () => setSnackbarOpen(false);

  const stopRecording = async (source) => {
    const speechService = source === 'system' ? systemSpeechService : micSpeechService;
    const recognizer = source === 'system' ? systemRecognizer : micRecognizer;

    try {
      // Stop new speech service if available
      if (speechService) {
        await speechService.stop();
      }

      // Fallback to legacy Azure recognizer if still in use
      if (recognizer && typeof recognizer.stopContinuousRecognitionAsync === 'function') {
        await recognizer.stopContinuousRecognitionAsync();
        if (recognizer.audioConfig && recognizer.audioConfig.privSource && recognizer.audioConfig.privSource.privStream) {
          const stream = recognizer.audioConfig.privSource.privStream;
          if (stream instanceof MediaStream) {
            stream.getTracks().forEach(track => {
              track.stop();
            });
          }
        }
        if (recognizer.audioConfig && typeof recognizer.audioConfig.close === 'function') {
          recognizer.audioConfig.close();
        }
      }
    } catch (error) {
      console.error(`Error stopping ${source} recognition:`, error);
      showSnackbar(`Error stopping ${source} audio: ${error.message}`, 'error');
    } finally {
      if (source === 'system') {
        setIsSystemAudioActive(false);
        setSystemRecognizer(null);
        setSystemSpeechService(null);
      } else {
        setIsMicrophoneActive(false);
        setMicRecognizer(null);
        setMicSpeechService(null);
        setActiveSpeechService(null); // Clear active service when stopping microphone
      }
    }
  };

  const handleClearSystemTranscription = () => {
    finalTranscript.current.system = '';
    systemInterimTranscription.current = '';
    dispatch(clearTranscription());
  };

  const handleClearMicTranscription = () => {
    finalTranscript.current.microphone = '';
    micInterimTranscription.current = '';
    setMicTranscription('');
  };

  const handleTranscriptionEvent = (text, source) => {
    const cleanText = text.replace(/\s+/g, ' ').trim();
    if (!cleanText) return;

    finalTranscript.current[source] += cleanText + ' ';

    if (source === 'system') {
      dispatch(setTranscription(finalTranscript.current.system + systemInterimTranscription.current));
    } else {
      setMicTranscription(finalTranscript.current.microphone + micInterimTranscription.current);
    }

    const currentConfig = getConfig();
    const currentSilenceTimerDuration = currentConfig.silenceTimerDuration;

    if ((source === 'system' && systemAutoModeRef.current) || (source === 'microphone' && !isManualModeRef.current)) {
      clearTimeout(silenceTimer.current);
      silenceTimer.current = setTimeout(() => {
        askOpenAI(finalTranscript.current[source].trim(), source);
      }, currentSilenceTimerDuration * 1000);
    }
  };

  const handleManualInputChange = (value, source) => {
    if (source === 'system') {
      dispatch(setTranscription(value));
      finalTranscript.current.system = value;
    } else {
      setMicTranscription(value);
      finalTranscript.current.microphone = value;
    }
  };

  const handleManualSubmit = (source) => {
    const textToSubmit = source === 'system' ? transcriptionFromStore : micTranscription;
    if (textToSubmit.trim()) {
      askOpenAI(textToSubmit.trim(), source);
    } else {
      showSnackbar('Input is empty.', 'warning');
    }
  };

  const handleKeyPress = (e, source) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleManualSubmit(source);
    }
  };



  // Legacy Azure recognizer function for fallback
  const createLegacyAzureRecognizer = async (mediaStream, source) => {
    const currentConfig = getConfig();
    if (!currentConfig.azureToken || !currentConfig.azureRegion) {
      throw new Error('Azure Speech credentials missing');
    }

    let audioConfig;
    try {
      audioConfig = SpeechSDK.AudioConfig.fromStreamInput(mediaStream);
    } catch (configError) {
      console.error(`Error creating AudioConfig for ${source}:`, configError);
      throw new Error(`Error setting up audio for ${source}: ${configError.message}`);
    }

    const speechConfig = SpeechSDK.SpeechConfig.fromSubscription(currentConfig.azureToken, currentConfig.azureRegion);
    speechConfig.speechRecognitionLanguage = currentConfig.azureLanguage;

    const recognizer = new SpeechSDK.SpeechRecognizer(speechConfig, audioConfig);

    recognizer.recognizing = (s, e) => {
      if (e.result.reason === SpeechSDK.ResultReason.RecognizingSpeech) {
        const interimText = e.result.text;
        if (source === 'system') {
          systemInterimTranscription.current = interimText;
          dispatch(setTranscription(finalTranscript.current.system + interimText));
        } else {
          micInterimTranscription.current = interimText;
          setMicTranscription(finalTranscript.current.microphone + interimText);
        }
      }
    };

    recognizer.recognized = (s, e) => {
      if (e.result.reason === SpeechSDK.ResultReason.RecognizedSpeech && e.result.text) {
        if (source === 'system') systemInterimTranscription.current = '';
        else micInterimTranscription.current = '';
        handleTranscriptionEvent(e.result.text, source);
      }
    };

    recognizer.canceled = (s, e) => {
      console.log(`CANCELED: Reason=${e.reason} for ${source}`);
      if (e.reason === SpeechSDK.CancellationReason.Error) {
        console.error(`CANCELED: ErrorCode=${e.errorCode}`);
        console.error(`CANCELED: ErrorDetails=${e.errorDetails}`);
        showSnackbar(`Speech recognition error for ${source}: ${e.errorDetails}`, 'error');
      }
      stopRecording(source);
    };

    recognizer.sessionStopped = (s, e) => {
      console.log(`Session stopped event for ${source}.`);
      stopRecording(source);
    };

    try {
      await recognizer.startContinuousRecognitionAsync();
      showSnackbar(`${source === 'system' ? 'System audio' : 'Microphone'} recording started (Azure Speech).`, 'success');
      return recognizer;
    } catch (error) {
      console.error(`Error starting ${source} continuous recognition:`, error);
      if (audioConfig && typeof audioConfig.close === 'function') audioConfig.close();
      throw error;
    }
  };

  const createSpeechRecognizer = async (mediaStream, source) => {
    const currentConfig = getConfig();

    // Create speech service callbacks
    const callbacks = {
      onStart: (source) => {
        console.log(`Speech recognition started for ${source}`);
        showSnackbar(`${source === 'system' ? 'System audio' : 'Microphone'} recording started.`, 'success');
      },

      onInterimResult: (text, source) => {
        console.log(`Interim result for ${source}:`, text);
        if (source === 'system') {
          systemInterimTranscription.current = text;
          dispatch(setTranscription(finalTranscript.current.system + text));
        } else {
          micInterimTranscription.current = text;
          setMicTranscription(finalTranscript.current.microphone + text);
        }
      },

      onFinalResult: (text, source) => {
        console.log(`Final result for ${source}:`, text);
        // Clear interim transcription
        if (source === 'system') systemInterimTranscription.current = '';
        else micInterimTranscription.current = '';

        // Handle the final transcription
        handleTranscriptionEvent(text, source);
      },

      onError: (error, source) => {
        console.error(`Speech recognition error for ${source}:`, error);
        showSnackbar(`Speech recognition error for ${source}: ${error.message}`, 'error');
        stopRecording(source);
      },

      onStop: (source) => {
        console.log(`Speech recognition stopped for ${source}`);
      }
    };

    try {
      // Check if we have the required credentials for the selected service
      const selectedService = currentConfig.speechService || 'deepgram';
      let configToUse = currentConfig;

      if (selectedService === 'deepgram' && !currentConfig.deepgramKey) {
        // If Deepgram is selected but no key is provided, check if Azure is available
        if (currentConfig.azureToken && currentConfig.azureRegion) {
          console.log('Deepgram key missing, falling back to Azure Speech Services');
          configToUse = { ...currentConfig, speechService: 'azure' };
          showSnackbar('Using Azure Speech Services (Deepgram key not configured)', 'info');
        } else {
          throw new Error('Deepgram API key is required. Get a free API key from deepgram.com and configure it in Settings (⚙️ icon).');
        }
      } else if (selectedService === 'azure' && (!currentConfig.azureToken || !currentConfig.azureRegion)) {
        // If Azure is selected but credentials are missing, check if Deepgram is available
        if (currentConfig.deepgramKey) {
          console.log('Azure credentials missing, falling back to Deepgram');
          configToUse = { ...currentConfig, speechService: 'deepgram' };
          showSnackbar('Using Deepgram (Azure credentials not configured)', 'info');
        } else {
          throw new Error('Azure Speech credentials are required. Please configure them in Settings, or provide Deepgram API key for fallback.');
        }
      }

      // Create the appropriate speech service
      const speechService = createSpeechService(configToUse, callbacks);

      // Track which service is being used
      setActiveSpeechService(configToUse.speechService);

      // Start the speech service
      await speechService.start(mediaStream, source);

      // Show which service is active
      const serviceName = configToUse.speechService === 'deepgram' ? 'Deepgram' : 'Azure Speech';
      showSnackbar(`Using ${serviceName} for ${source === 'system' ? 'system audio' : 'microphone'}`, 'info');

      return speechService;
    } catch (error) {
      console.error(`Error creating speech service for ${source}:`, error);

      // If Deepgram fails and we have Azure credentials, try Azure Speech Service first
      if (configToUse.speechService === 'deepgram' && currentConfig.azureToken && currentConfig.azureRegion) {
        try {
          console.log(`Deepgram failed, trying Azure Speech Service for ${source}`);

          // Create Azure service configuration
          const azureConfig = { ...currentConfig, speechService: 'azure' };
          const azureSpeechService = createSpeechService(azureConfig, callbacks);

          // Track which service is being used
          setActiveSpeechService('azure');

          // Start the Azure speech service
          await azureSpeechService.start(mediaStream, source);

          showSnackbar(`Switched to Azure Speech Services (Deepgram connection failed)`, 'warning');
          return azureSpeechService;
        } catch (azureError) {
          console.error(`Azure Speech Service also failed for ${source}:`, azureError);
        }
      }

      // Try legacy Azure recognizer as final fallback
      if (currentConfig.azureToken && currentConfig.azureRegion) {
        try {
          console.log(`Attempting legacy Azure fallback for ${source}`);
          const legacyRecognizer = await createLegacyAzureRecognizer(mediaStream, source);
          showSnackbar(`Using legacy Azure Speech Services`, 'warning');
          return legacyRecognizer;
        } catch (legacyError) {
          console.error(`Legacy Azure fallback also failed for ${source}:`, legacyError);
        }
      }

      // Provide helpful error messages
      if (error.message.includes('API key') || error.message.includes('credentials')) {
        showSnackbar(`${error.message}`, 'error');
      } else if (error.message.includes('Deepgram')) {
        showSnackbar(`${error.message}`, 'error');
      } else {
        showSnackbar(`Failed to start ${source} recognition: ${error.message}`, 'error');
      }

      mediaStream.getTracks().forEach(track => track.stop());
      return null;
    }
  };

  const startSystemAudioRecognition = async () => {
    if (isSystemAudioActive) {
      await stopRecording('system');
      return;
    }

    if (!navigator.mediaDevices || !navigator.mediaDevices.getDisplayMedia) {
      showSnackbar('Screen sharing is not supported by your browser.', 'error');
      setIsSystemAudioActive(false);
      return;
    }

    try {
      const mediaStream = await navigator.mediaDevices.getDisplayMedia({
        audio: true,
        video: {
          displaySurface: 'browser',
          logicalSurface: true
        }
      });

      const audioTracks = mediaStream.getAudioTracks();
      if (audioTracks.length === 0) {
        showSnackbar('No audio track detected. Please ensure you share a tab with audio.', 'warning');
        mediaStream.getTracks().forEach(track => track.stop());
        return;
      }

      if (systemRecognizer) {
        await stopRecording('system');
      }

      const speechServiceInstance = await createSpeechRecognizer(mediaStream, 'system');
      if (speechServiceInstance) {
        // Check if it's a new speech service or legacy recognizer
        if (speechServiceInstance.start && speechServiceInstance.stop) {
          // New speech service
          setSystemSpeechService(speechServiceInstance);
        } else {
          // Legacy Azure recognizer
          setSystemRecognizer(speechServiceInstance);
        }
        setIsSystemAudioActive(true);
        mediaStream.getTracks().forEach(track => {
          track.onended = () => {
            showSnackbar('Tab sharing ended.', 'info');
            stopRecording('system');
          };
        });
      } else {
        mediaStream.getTracks().forEach(track => track.stop());
      }
    } catch (error) {
      console.error('System audio capture error:', error);
      if (error.name === "NotAllowedError") {
        showSnackbar('Permission denied for screen recording. Please allow access.', 'error');
      } else if (error.name === "NotFoundError") {
        showSnackbar('No suitable tab/window found to share.', 'error');
      } else if (error.name === "NotSupportedError") {
        showSnackbar('System audio capture not supported by your browser.', 'error');
      } else {
        showSnackbar(`Failed to start system audio capture: ${error.message || 'Unknown error'}`, 'error');
      }
      setIsSystemAudioActive(false);
    }
  };

  const startMicrophoneRecognition = async () => {
    if (isMicrophoneActive) {
      await stopRecording('microphone');
      return;
    }
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
      if (micSpeechService) await stopRecording('microphone');

      const speechServiceInstance = await createSpeechRecognizer(mediaStream, 'microphone');
      if (speechServiceInstance) {
        // Check if it's a new speech service or legacy recognizer
        if (speechServiceInstance.start && speechServiceInstance.stop) {
          // New speech service
          setMicSpeechService(speechServiceInstance);
        } else {
          // Legacy Azure recognizer
          setMicRecognizer(speechServiceInstance);
        }
        setIsMicrophoneActive(true);
      } else {
        mediaStream.getTracks().forEach(track => track.stop());
      }
    } catch (error) {
      console.error('Microphone capture error:', error);
      if (error.name === "NotAllowedError" || error.name === "NotFoundError") {
        showSnackbar('Permission denied for microphone. Please allow access.', 'error');
      } else {
        showSnackbar(`Failed to access microphone: ${error.message || 'Unknown error'}`, 'error');
      }
      setIsMicrophoneActive(false);
    }
  };

  // Helper function to determine reasoning effort for future advanced models
  const getReasoningEffort = (text, source) => {
    const textLength = text.length;
    const isComplexQuestion = text.includes('?') && textLength > 100;
    const isCombined = source === 'combined';

    if (isCombined || isComplexQuestion || textLength > 500) return 'high';
    if (textLength > 200) return 'medium';
    return 'low';
  };

  const askOpenAI = async (text, source) => {
    if (!text.trim()) {
      showSnackbar('No input text to process.', 'warning');
      return;
    }
    if (!openAI || isAILoading) {
      showSnackbar('AI client is not ready. Please wait or check settings.', 'warning');
      return;
    }

    const currentConfig = getConfig();
    const lengthSettings = {
      concise: { temperature: 0.4, maxTokens: 250 },
      medium: { temperature: 0.6, maxTokens: 500 },
      lengthy: { temperature: 0.8, maxTokens: 1000 }
    };
    const { temperature, maxTokens } = lengthSettings[currentConfig.responseLength || 'medium'];

    setIsProcessing(true);
    const timestamp = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    let streamedResponse = '';

    dispatch(addToHistory({ type: 'question', text, timestamp, source, status: 'pending' }));
    dispatch(setAIResponse(''));

    try {
      const conversationHistoryForAPI = history
        .filter(e => e.text && (e.type === 'question' || e.type === 'response') && e.status !== 'pending')
        .slice(-6)
        .map(event => ({
          role: event.type === 'question' ? 'user' : 'assistant',
          content: event.text,
        }));

      if (currentConfig.aiModel.startsWith('gemini')) {
        // Enhanced Gemini API configuration for 2.5 models
        const modelConfig = {
          model: currentConfig.aiModel,
          generationConfig: {
            temperature,
            maxOutputTokens: maxTokens
          },
          systemInstruction: { parts: [{ text: getEnhancedSystemPrompt(currentConfig.gptSystemPrompt) }] }
        };

        // Add thinking configuration for Gemini 2.5 models
        if (currentConfig.aiModel.includes('2.5') && currentConfig.thinkingBudget !== undefined) {
          if (currentConfig.thinkingBudget === 0) {
            // Disable thinking
            modelConfig.generationConfig.thinkingConfig = { thinkingBudget: 0 };
          } else if (currentConfig.thinkingBudget > 0) {
            // Custom thinking budget
            modelConfig.generationConfig.thinkingConfig = { thinkingBudget: currentConfig.thinkingBudget };
          }
          // If thinkingBudget is null, use default (thinking enabled)
        }

        // Use the new @google/genai API
        const messages = conversationHistoryForAPI.map(msg => ({
          role: msg.role === 'user' ? 'user' : 'model',
          content: msg.content
        }));

        // Add the current user message
        messages.push({ role: 'user', content: userMessage });

        // Use the new @google/genai streaming API
        const stream = await openAI.models.generateContentStream({
          model: currentConfig.aiModel.replace('gemini-', ''), // Remove prefix if present
          messages: messages,
          ...modelConfig.generationConfig
        });

        for await (const chunk of stream) {
          if (chunk && chunk.content) {
            streamedResponse += chunk.content;
            if (throttledDispatchSetAIResponseRef.current) {
              throttledDispatchSetAIResponseRef.current(streamedResponse);
            }
          }
        }
      } else {
        // Enhanced OpenAI API usage with future-ready parameters
        const messages = [
          { role: 'system', content: getEnhancedSystemPrompt(currentConfig.gptSystemPrompt) },
          ...conversationHistoryForAPI,
          { role: 'user', content: text }
        ];

        const requestParams = {
          model: currentConfig.aiModel,
          messages,
          stream: true,
        };

        // Set temperature based on model capabilities
        if (currentConfig.aiModel.startsWith('o1')) {
          // o1 models don't support temperature parameter at all
          // Don't set temperature for o1 models
        } else if (currentConfig.aiModel.startsWith('gpt-5')) {
          // GPT-5 models may have temperature restrictions, use default value
          requestParams.temperature = 1;
        } else {
          // Standard models support configurable temperature
          requestParams.temperature = temperature;
        }

        // Use the correct token parameter based on model
        if (currentConfig.aiModel.startsWith('gpt-5') || currentConfig.aiModel.startsWith('o1')) {
          requestParams.max_completion_tokens = maxTokens;
        } else {
          requestParams.max_tokens = maxTokens;
        }

        // Add model-specific parameters for different model types
        if (currentConfig.aiModel.startsWith('gpt-5')) {
          // GPT-5 models support new parameters
          if (currentConfig.reasoningEffort) {
            requestParams.reasoning_effort = currentConfig.reasoningEffort;
          }
          if (currentConfig.verbosity !== undefined) {
            requestParams.verbosity = currentConfig.verbosity === 0 ? 'low' :
                                     currentConfig.verbosity === 1 ? 'medium' : 'high';
          }
        } else if (currentConfig.aiModel.startsWith('o1')) {
          // o1 models use different parameters and don't support streaming
          // Remove streaming for o1 models
          requestParams.stream = false;

          // o1 models don't use system messages in the same way
          // Move system prompt to the first user message
          requestParams.messages = [
            { role: 'user', content: `${getEnhancedSystemPrompt(currentConfig.gptSystemPrompt)}\n\n${text}` },
            ...conversationHistoryForAPI.slice(1) // Skip the system message
          ];

          // o1 models require temperature = 1 (already set above)
          // No additional temperature modification needed
        }

        if (currentConfig.aiModel.startsWith('o1')) {
          // o1 models don't support streaming, handle as single response
          const response = await openAI.chat.completions.create(requestParams);
          streamedResponse = response.choices[0]?.message?.content || '';
          if (throttledDispatchSetAIResponseRef.current) {
            throttledDispatchSetAIResponseRef.current(streamedResponse);
          }
        } else {
          // Standard streaming for GPT-5, GPT-4o, and other models
          const stream = await openAI.chat.completions.create(requestParams);

          for await (const chunk of stream) {
            const chunkText = chunk.choices[0]?.delta?.content || '';
            streamedResponse += chunkText;
            if (throttledDispatchSetAIResponseRef.current) {
              throttledDispatchSetAIResponseRef.current(streamedResponse);
            }
          }
        }
      }
      if (throttledDispatchSetAIResponseRef.current && typeof throttledDispatchSetAIResponseRef.current.cancel === 'function') {
        throttledDispatchSetAIResponseRef.current.cancel();
      }
      dispatch(setAIResponse(streamedResponse));

      const finalTimestamp = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      dispatch(addToHistory({ type: 'response', text: streamedResponse, timestamp: finalTimestamp, status: 'completed' }));

    } catch (error) {
      console.error("AI request error:", error);
      const errorMessage = `AI request failed: ${error.message || 'Unknown error'}`;
      showSnackbar(errorMessage, 'error');
      dispatch(setAIResponse(`Error: ${errorMessage}`));
      dispatch(addToHistory({ type: 'response', text: `Error: ${errorMessage}`, timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }), status: 'error' }));
    } finally {
      if ((source === 'system' && systemAutoModeRef.current) || (source === 'microphone' && !isManualModeRef.current)) {
        finalTranscript.current[source] = '';
        if (source === 'system') {
          systemInterimTranscription.current = '';
          dispatch(setTranscription(''));
        } else {
          micInterimTranscription.current = '';
          setMicTranscription('');
        }
      }
      setIsProcessing(false);
    }
  };

  const formatAndDisplayResponse = useCallback((response) => {
    if (!response) return null;
    return (
      <ReactMarkdown
        components={{
          code({ node, inline, className, children, ...props }) {
            const match = /language-(\w+)/.exec(className || '');
            return !inline && match ? (
              <Box sx={{
                my: 1,
                position: 'relative',
                '& pre': {
                  borderRadius: '4px',
                  padding: '12px !important',
                  fontSize: '0.875rem',
                  overflowX: 'auto',
                  whiteSpace: 'pre-wrap',
                  wordBreak: 'break-all',
                }
              }}>
                <pre><code className={className} {...props} dangerouslySetInnerHTML={{ __html: hljs.highlight(String(children).replace(/\n$/, ''), { language: match[1], ignoreIllegals: true }).value }} /></pre>
              </Box>
            ) : (
              <code
                className={className}
                {...props}
                style={{
                  backgroundColor: 'rgba(0,0,0,0.05)',
                  padding: '2px 4px',
                  borderRadius: '4px',
                  fontFamily: 'monospace',
                  fontSize: '0.875rem',
                  wordBreak: 'break-all'
                }}
              >
                {children}
              </code>
            );
          },
          p: ({ node, ...props }) => <Typography paragraph {...props} sx={{ mb: 1, fontSize: '0.95rem', wordBreak: 'break-word' }} />,
          strong: ({ node, ...props }) => <Typography component="strong" fontWeight="bold" {...props} />,
          em: ({ node, ...props }) => <Typography component="em" fontStyle="italic" {...props} />,
          ul: ({ node, ...props }) => <Typography component="ul" sx={{ pl: 2.5, mb: 1, fontSize: '0.95rem', wordBreak: 'break-word' }} {...props} />,
          ol: ({ node, ...props }) => <Typography component="ol" sx={{ pl: 2.5, mb: 1, fontSize: '0.95rem', wordBreak: 'break-word' }} {...props} />,
          li: ({ node, ...props }) => <Typography component="li" sx={{ mb: 0.25, fontSize: '0.95rem', wordBreak: 'break-word' }} {...props} />,
        }}
      >
        {response}
      </ReactMarkdown>
    );
  }, []);

  const renderHistoryItem = (item, index) => {
    if (item.type !== 'response') return null;
    const Icon = SmartToyOutlinedIcon;
    const title = 'AI Assistant';
    const avatarBgColor = theme.palette.secondary.light;

    return (
      <ListItem key={`response-${index}`} sx={{ alignItems: 'flex-start', px: 0, py: 1.5 }}>
        <Avatar sx={{ bgcolor: avatarBgColor, mr: 2, mt: 0.5 }}>
          <Icon sx={{ color: theme.palette.getContrastText(avatarBgColor) }} />
        </Avatar>
        <Paper variant="outlined" sx={{ p: 1.5, flexGrow: 1, bgcolor: theme.palette.background.default, borderColor: theme.palette.divider, overflowX: 'auto' }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
            <Typography variant="subtitle2" fontWeight="bold">{title}</Typography>
            <Typography variant="caption" color="text.secondary">{item.timestamp}</Typography>
          </Box>
          {formatAndDisplayResponse(item.text)}
        </Paper>
      </ListItem>
    );
  };



  const handleSortOrderToggle = () => {
    setAiResponseSortOrder(prev => prev === 'newestAtBottom' ? 'newestAtTop' : 'newestAtBottom');
  };

  const getAiResponsesToDisplay = () => {
    let responses = history.filter(item => item.type === 'response').slice();
    const currentStreamingText = aiResponseFromStore;

    if (isProcessing && currentStreamingText && currentStreamingText.trim() !== '') {
      responses.push({ text: currentStreamingText, timestamp: 'Streaming...', type: 'current_streaming' });
    }

    if (aiResponseSortOrder === 'newestAtTop') {
      return responses.reverse();
    }
    return responses;
  };

  const togglePipWindow = async () => {
    if (isPipWindowActive) {
      if (documentPipWindowRef.current && typeof documentPipWindowRef.current.close === 'function') {
        try {
          await documentPipWindowRef.current.close();
        } catch (e) { console.error("Error closing document PiP window:", e); }
      } else if (pipWindowRef.current && !pipWindowRef.current.closed) {
        pipWindowRef.current.close();
      }
      return; // State update will be handled by pagehide/interval listeners
    }

    const addResizeListener = (pipWindow) => {
      const handlePipResize = debounce(() => {
        if (!pipWindow || (pipWindow.closed)) return;
        const target = documentPipIframeRef.current ? documentPipIframeRef.current.contentWindow : pipWindow;
        if (target) {
          target.postMessage({
            type: 'PIP_RESIZE',
            payload: {
              width: pipWindow.innerWidth,
              height: pipWindow.innerHeight
            }
          }, '*');
        }
      }, 50);

      pipWindow.addEventListener('resize', handlePipResize);
      return () => pipWindow.removeEventListener('resize', handlePipResize); // Return a cleanup function
    };

    if (window.documentPictureInPicture && typeof window.documentPictureInPicture.requestWindow === 'function') {
      try {
        const pipOptions = { width: 400, height: 300 };
        const requestedPipWindow = await window.documentPictureInPicture.requestWindow(pipOptions);
        documentPipWindowRef.current = requestedPipWindow;
        setIsPipWindowActive(true);

        const iframe = documentPipWindowRef.current.document.createElement('iframe');
        iframe.src = '/pip-log';
        iframe.style.width = '100%';
        iframe.style.height = '100%';
        iframe.style.border = 'none';
        documentPipWindowRef.current.document.body.style.margin = '0';
        documentPipWindowRef.current.document.body.style.overflow = 'hidden';
        documentPipWindowRef.current.document.body.append(iframe);
        documentPipIframeRef.current = iframe;

        const removeResizeListener = addResizeListener(documentPipWindowRef.current);

        iframe.onload = () => {
          if (documentPipIframeRef.current && documentPipIframeRef.current.contentWindow) {
            documentPipIframeRef.current.contentWindow.postMessage({
              type: 'AI_LOG_DATA',
              payload: {
                historicalResponses: history.filter(item => item.type === 'response'),
                currentStreamingText: isProcessing ? aiResponseFromStore : '',
                isProcessing: isProcessing,
                sortOrder: aiResponseSortOrder
              }
            }, '*');
          }
        };

        documentPipWindowRef.current.addEventListener('pagehide', () => {
          removeResizeListener();
          setIsPipWindowActive(false);
          documentPipWindowRef.current = null;
          documentPipIframeRef.current = null;
        });

        showSnackbar('Native PiP window opened.', 'success');
        return;

      } catch (err) {
        console.error('Document Picture-in-Picture API error:', err);
        showSnackbar(`Native PiP not available or failed. Trying popup. (${err.message})`, 'warning');
      }
    }

    pipWindowRef.current = window.open('/pip-log', 'AIResponsePiP', 'width=400,height=550,resizable=yes,scrollbars=yes,status=no,toolbar=no,menubar=no,location=no,noopener,noreferrer,popup=yes');

    if (pipWindowRef.current) {
      setIsPipWindowActive(true);
      const removeResizeListener = addResizeListener(pipWindowRef.current);

      pipWindowRef.current.onload = () => {
        if (pipWindowRef.current && !pipWindowRef.current.closed) {
          pipWindowRef.current.postMessage({
            type: 'AI_LOG_DATA',
            payload: {
              historicalResponses: history.filter(item => item.type === 'response'),
              currentStreamingText: isProcessing ? aiResponseFromStore : '',
              isProcessing: isProcessing,
              sortOrder: aiResponseSortOrder
            }
          }, '*');
        }
      };
      const pipCheckInterval = setInterval(() => {
        if (pipWindowRef.current && pipWindowRef.current.closed) {
          clearInterval(pipCheckInterval);
          removeResizeListener();
          setIsPipWindowActive(false);
          pipWindowRef.current = null;
        }
      }, 500);
      if (pipWindowRef.current) pipWindowRef.current._pipIntervalId = pipCheckInterval;
    } else {
      showSnackbar('Failed to open PiP window. Please check popup blocker settings.', 'error');
      setIsPipWindowActive(false);
    }
  };

  useEffect(() => {
    return () => {
      if (pipWindowRef.current && pipWindowRef.current._pipIntervalId) {
        clearInterval(pipWindowRef.current._pipIntervalId);
      }
      if (documentPipWindowRef.current && typeof documentPipWindowRef.current.close === 'function') {
        try { documentPipWindowRef.current.close(); } catch (e) { /*ignore*/ }
      }
    };
  }, []);

  useEffect(() => {
    let targetWindowForMessage = null;

    if (documentPipWindowRef.current && documentPipIframeRef.current && documentPipIframeRef.current.contentWindow) {
      targetWindowForMessage = documentPipIframeRef.current.contentWindow;
    } else if (pipWindowRef.current && !pipWindowRef.current.closed) {
      targetWindowForMessage = pipWindowRef.current;
    }

    if (isPipWindowActive && targetWindowForMessage) {
      try {
        targetWindowForMessage.postMessage({
          type: 'AI_LOG_DATA',
          payload: {
            historicalResponses: history.filter(item => item.type === 'response'),
            currentStreamingText: isProcessing ? aiResponseFromStore : '',
            isProcessing: isProcessing,
            sortOrder: aiResponseSortOrder
          }
        }, '*');
      } catch (e) {
        console.warn("Could not post message to PiP window:", e);
      }
    }
  }, [history, aiResponseFromStore, isPipWindowActive, aiResponseSortOrder, isProcessing]);

  return (
    <>
      <Head>
        <title>MyCopilot - Active Session</title>
      </Head>
      <Box sx={{
        display: 'flex',
        flexDirection: 'column',
        height: '100vh',
        bgcolor: 'grey.50',
        overflow: 'hidden'
      }}>
        {/* Modern Header */}
        <Box sx={{
          bgcolor: 'white',
          borderBottom: 1,
          borderColor: 'divider',
          px: 2,
          py: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
          minHeight: 'auto'
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
            <Box sx={{
              p: 1,
              borderRadius: 1.5,
              bgcolor: 'primary.main',
              color: 'white',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <SmartToyOutlinedIcon fontSize="small" />
            </Box>
            <Box>
              <Typography variant="h6" fontWeight="600" color="text.primary" sx={{ fontSize: '1.1rem' }}>
                MyCopilot
                {(resumeText || jobDescriptionText) && (
                  <Chip
                    label="Context Active"
                    color="primary"
                    size="small"
                    sx={{ ml: 1, fontSize: '0.7rem' }}
                  />
                )}
              </Typography>
              <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                AI-powered assistant
                {(resumeText || jobDescriptionText) && (
                  <span style={{ color: 'green', marginLeft: '8px' }}>
                    • {resumeText ? 'Resume' : ''}{resumeText && jobDescriptionText ? ' & ' : ''}{jobDescriptionText ? 'Job Description' : ''} loaded
                  </span>
                )}
              </Typography>
            </Box>
          </Box>

          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Upload Resume & Job Description">
              <IconButton
                onClick={() => setShowDocumentUpload(true)}
                sx={{
                  bgcolor: resumeText || jobDescriptionText ? 'primary.100' : 'grey.100',
                  color: resumeText || jobDescriptionText ? 'primary.main' : 'inherit',
                  '&:hover': {
                    bgcolor: resumeText || jobDescriptionText ? 'primary.200' : 'grey.200'
                  }
                }}
              >
                <UploadFileOutlinedIcon />
              </IconButton>
            </Tooltip>

            <Tooltip title="Question History">
              <IconButton
                onClick={() => window.open('/questions', '_blank')}
                sx={{
                  bgcolor: 'grey.100',
                  '&:hover': { bgcolor: 'grey.200' }
                }}
              >
                <QuizOutlinedIcon />
              </IconButton>
            </Tooltip>

            <Tooltip title="Settings">
              <IconButton
                onClick={() => setSettingsOpen(true)}
                sx={{
                  bgcolor: 'grey.100',
                  '&:hover': { bgcolor: 'grey.200' }
                }}
              >
                <SettingsOutlinedIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* Main Content Area */}
        <Box sx={{
          flexGrow: 1,
          p: 1,
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
          minHeight: 0 // Important for flex children to shrink
        }}>
          <Grid container spacing={1} sx={{
            flexGrow: 1,
            height: '100%',
            minHeight: 0 // Important for proper height calculation
          }}>
            {/* Left Panel - System Audio + Your Response */}
            <Grid item xs={12} md={4} sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: 1,
              height: '100%',
              minHeight: 0
            }}>
              <Card sx={{
                border: '1px solid',
                borderColor: 'divider',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                borderRadius: 2,
                height: 'auto',
                maxHeight: '45%'
              }}>
                <CardHeader
                  title="System Audio"
                  subheader="Capture system audio"
                  avatar={
                    <Box sx={{
                      p: 0.6,
                      borderRadius: 1.5,
                      bgcolor: 'info.main',
                      color: 'white',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      <DesktopWindowsOutlinedIcon fontSize="small" />
                    </Box>
                  }
                  sx={{
                    pb: 0,
                    pt: 1,
                    px: 2,
                    '& .MuiCardHeader-content': {
                      '& .MuiTypography-h5': {
                        fontSize: '1rem',
                        fontWeight: 600
                      },
                      '& .MuiTypography-body2': {
                        fontSize: '0.75rem'
                      }
                    }
                  }}
                />
                <CardContent sx={{ pt: 0, pb: 1, px: 2 }}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={systemAutoMode}
                        onChange={e => setSystemAutoMode(e.target.checked)}
                        color="primary"
                        size="small"
                      />
                    }
                    label="Auto-Submit"
                    sx={{ mb: 1, ml: 0 }}
                  />

                  <TextField
                    fullWidth
                    multiline
                    rows={2}
                    variant="outlined"
                    value={transcriptionFromStore}
                    onChange={(e) => handleManualInputChange(e.target.value, 'system')}
                    onKeyDown={(e) => handleKeyPress(e, 'system')}
                    placeholder="System audio will appear here..."
                    sx={{
                      mb: 1,
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 1.5,
                        fontSize: '0.875rem'
                      },
                      '& .MuiInputBase-input': {
                        py: 1
                      }
                    }}
                  />

                  <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
                    <Button
                      onClick={startSystemAudioRecognition}
                      variant={isSystemAudioActive ? "contained" : "outlined"}
                      color={isSystemAudioActive ? 'error' : 'info'}
                      startIcon={isSystemAudioActive ? <StopOutlinedIcon /> : <DesktopWindowsOutlinedIcon />}
                      size="small"
                      sx={{
                        flexGrow: 1,
                        py: 0.75,
                        fontWeight: 500,
                        borderRadius: 1.5,
                        textTransform: 'none',
                        fontSize: '0.875rem'
                      }}
                    >
                      {isSystemAudioActive ? 'Stop' : 'Start Capture'}
                    </Button>

                    <Tooltip title="Clear">
                      <IconButton
                        onClick={handleClearSystemTranscription}
                        size="small"
                        sx={{
                          bgcolor: 'grey.100',
                          '&:hover': { bgcolor: 'grey.200' },
                          borderRadius: 1.5,
                          p: 0.75
                        }}
                      >
                        <ClearOutlinedIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Box>

                  {!systemAutoMode && (
                    <Button
                      onClick={() => handleManualSubmit('system')}
                      variant="contained"
                      color="primary"
                      startIcon={<SendOutlinedIcon />}
                      disabled={isProcessing || !transcriptionFromStore.trim()}
                      fullWidth
                      size="small"
                      sx={{
                        py: 0.75,
                        fontWeight: 500,
                        borderRadius: 1.5,
                        textTransform: 'none',
                        mb: 0.5,
                        fontSize: '0.875rem'
                      }}
                    >
                      Submit
                    </Button>
                  )}

                  <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem', lineHeight: 1.2 }}>
                    {isSystemAudioActive ?
                      '🔴 Recording...' :
                      'Select "Chrome Tab" + "Share audio"'
                    }
                  </Typography>
                </CardContent>
              </Card>

              {/* Your Response Section - Moved from right panel */}
              <Card sx={{
                border: '1px solid',
                borderColor: 'divider',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                borderRadius: 2,
                flexGrow: 1,
                display: 'flex',
                flexDirection: 'column',
                minHeight: 0
              }}>
                <CardHeader
                  title="Your Response"
                  subheader="Voice or text input"
                  avatar={
                    <Box sx={{
                      p: 0.6,
                      borderRadius: 1.5,
                      bgcolor: isMicrophoneActive ? 'error.main' : 'primary.main',
                      color: 'white',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      {isMicrophoneActive ? <MicNoneOutlinedIcon fontSize="small" /> : <PersonOutlineIcon fontSize="small" />}
                    </Box>
                  }
                  action={
                    activeSpeechService && (
                      <Chip
                        label={activeSpeechService === 'deepgram' ? 'Deepgram' : 'Azure'}
                        size="small"
                        color={activeSpeechService === 'deepgram' ? 'primary' : 'secondary'}
                        variant="outlined"
                        sx={{
                          fontWeight: 500,
                          borderRadius: 1.5,
                          fontSize: '0.75rem'
                        }}
                      />
                    )
                  }
                  sx={{
                    pb: 0,
                    pt: 1,
                    px: 2,
                    '& .MuiCardHeader-content': {
                      '& .MuiTypography-h5': {
                        fontSize: '1rem',
                        fontWeight: 600
                      },
                      '& .MuiTypography-body2': {
                        fontSize: '0.75rem'
                      }
                    }
                  }}
                />
                <CardContent sx={{ pt: 0, pb: 1, px: 2, flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={isManualMode}
                        onChange={e => setIsManualMode(e.target.checked)}
                        color="primary"
                        size="small"
                      />
                    }
                    label="Manual Mode"
                    sx={{ mb: 1, ml: 0 }}
                  />

                  <TextField
                    fullWidth
                    multiline
                    rows={2}
                    variant="outlined"
                    value={micTranscription}
                    onChange={(e) => handleManualInputChange(e.target.value, 'microphone')}
                    onKeyDown={(e) => handleKeyPress(e, 'microphone')}
                    placeholder={isManualMode ? "Type your response..." : "Your speech will appear here..."}
                    sx={{
                      mb: 1,
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 1.5,
                        fontSize: '0.875rem'
                      },
                      '& .MuiInputBase-input': {
                        py: 1
                      }
                    }}
                  />

                  <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
                    <Button
                      onClick={startMicrophoneRecognition}
                      variant={isMicrophoneActive ? "contained" : "outlined"}
                      color={isMicrophoneActive ? 'error' : 'primary'}
                      startIcon={isMicrophoneActive ? <MicOffOutlinedIcon /> : <MicNoneOutlinedIcon />}
                      size="small"
                      sx={{
                        flexGrow: 1,
                        py: 0.75,
                        fontWeight: 500,
                        borderRadius: 1.5,
                        textTransform: 'none',
                        fontSize: '0.875rem'
                      }}
                    >
                      {isMicrophoneActive ? 'Stop' : 'Start Recording'}
                    </Button>

                    <Tooltip title="Clear">
                      <IconButton
                        onClick={handleClearMicTranscription}
                        size="small"
                        sx={{
                          bgcolor: 'grey.100',
                          '&:hover': { bgcolor: 'grey.200' },
                          borderRadius: 1.5,
                          p: 0.75
                        }}
                      >
                        <ClearOutlinedIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Box>

                  {isManualMode && (
                    <Button
                      onClick={() => handleManualSubmit('microphone')}
                      variant="contained"
                      color="primary"
                      startIcon={<SendOutlinedIcon />}
                      disabled={isProcessing || !micTranscription.trim()}
                      fullWidth
                      size="small"
                      sx={{
                        py: 0.75,
                        fontWeight: 500,
                        borderRadius: 1.5,
                        textTransform: 'none',
                        mb: 0.5,
                        fontSize: '0.875rem'
                      }}
                    >
                      Submit
                    </Button>
                  )}

                  <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem', lineHeight: 1.2 }}>
                    {isMicrophoneActive ?
                      '🎤 Recording...' :
                      isManualMode ?
                        'Type and submit' :
                        'Click to start recording'
                    }
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            {/* Right Panel - AI Assistant (Expanded) */}
            <Grid item xs={12} md={8} sx={{
              display: 'flex',
              flexDirection: 'column',
              height: '100%',
              minHeight: 0
            }}>
              <Card sx={{
                flexGrow: 1,
                display: 'flex',
                flexDirection: 'column',
                border: '1px solid',
                borderColor: 'divider',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                borderRadius: 2,
                height: '100%',
                minHeight: 0
              }}>
                <CardHeader
                  title="AI Assistant"
                  subheader="Real-time support"
                  avatar={
                    <Box sx={{
                      p: 0.8,
                      borderRadius: 1.5,
                      bgcolor: 'success.main',
                      color: 'white',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      <SmartToyOutlinedIcon fontSize="small" />
                    </Box>
                  }
                  action={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      <Tooltip title={isPipWindowActive ? "Close PiP" : "Open PiP"}>
                        <IconButton
                          onClick={togglePipWindow}
                          size="small"
                          color={isPipWindowActive ? "secondary" : "default"}
                          sx={{
                            bgcolor: isPipWindowActive ? 'secondary.light' : 'grey.100',
                            '&:hover': { bgcolor: isPipWindowActive ? 'secondary.main' : 'grey.200' },
                            borderRadius: 1.5
                          }}
                        >
                          <PictureInPictureAltOutlinedIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title={aiResponseSortOrder === 'newestAtTop' ? "Oldest First" : "Newest First"}>
                        <IconButton
                          onClick={handleSortOrderToggle}
                          size="small"
                          sx={{
                            bgcolor: 'grey.100',
                            '&:hover': { bgcolor: 'grey.200' },
                            borderRadius: 1.5
                          }}
                        >
                          {aiResponseSortOrder === 'newestAtTop' ? <ArrowDownwardIcon fontSize="small" /> : <ArrowUpwardIcon fontSize="small" />}
                        </IconButton>
                      </Tooltip>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={autoScroll}
                            onChange={(e) => setAutoScroll(e.target.checked)}
                            color="primary"
                            size="small"
                          />
                        }
                        label="Auto"
                        sx={{ ml: 0.5, '& .MuiFormControlLabel-label': { fontSize: '0.875rem' } }}
                      />
                    </Box>
                  }
                  sx={{ borderBottom: `1px solid ${theme.palette.divider}` }}
                />
                <CardContent sx={{
                  flexGrow: 1,
                  overflow: 'hidden',
                  p: 0,
                  minHeight: 0,
                  display: 'flex',
                  flexDirection: 'column'
                }}>
                  <ScrollToBottom
                    className="scroll-to-bottom"
                    mode={autoScroll ? (aiResponseSortOrder === 'newestAtTop' ? "top" : "bottom") : undefined}
                    followButtonClassName="hidden-follow-button"
                    style={{
                      height: '100%',
                      overflow: 'auto'
                    }}
                  >
                    <List sx={{ px: 2, py: 1 }}>
                      {getAiResponsesToDisplay().map(renderHistoryItem)}
                      {isProcessing && (
                        <ListItem sx={{ justifyContent: 'center', py: 2 }}>
                          <CircularProgress size={24} />
                          <Typography variant="caption" sx={{ ml: 1 }}>AI is thinking...</Typography>
                        </ListItem>
                      )}
                    </List>
                  </ScrollToBottom>
                </CardContent>
              </Card>
            </Grid>


          </Grid>
        </Box>

        <SettingsDialog
          open={settingsOpen}
          onClose={() => setSettingsOpen(false)}
          onSave={handleSettingsSaved}
        />

        {/* Document Upload Dialog */}
        <Dialog
          open={showDocumentUpload}
          onClose={() => setShowDocumentUpload(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            Resume & Job Description
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Load your resume and add job description to get personalized interview coaching
            </Typography>
          </DialogTitle>
          <DialogContent>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, mt: 2 }}>
              {/* Resume Selection */}
              <Box>
                <Typography variant="h6" gutterBottom>
                  Resume {resumeText && <Chip label="Loaded" color="success" size="small" sx={{ ml: 1 }} />}
                </Typography>

                {/* Resume from folder */}
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" gutterBottom>
                    Select from resume folder:
                  </Typography>
                  {availableResumes.length > 0 ? (
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      {availableResumes.map((resume) => (
                        <Button
                          key={resume.path}
                          variant={selectedResumeFile === resume.path ? "contained" : "outlined"}
                          onClick={() => loadResumeFromFolder(resume.path)}
                          sx={{ justifyContent: 'flex-start' }}
                        >
                          📄 {resume.name}
                          {selectedResumeFile === resume.path && " ✓"}
                        </Button>
                      ))}
                    </Box>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      No resume files found. Place your resume files in public/resumes/ folder
                    </Typography>
                  )}
                </Box>

                {/* Manual upload */}
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" gutterBottom>
                    Or upload a file:
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                    <Button
                      variant="outlined"
                      component="label"
                      startIcon={<UploadFileOutlinedIcon />}
                    >
                      Upload Resume
                      <input
                        type="file"
                        hidden
                        accept=".txt,.pdf,.doc,.docx"
                        onChange={handleResumeUpload}
                      />
                    </Button>
                    <Typography variant="body2" color="text.secondary">
                      Supports: .txt, .pdf, .doc, .docx
                    </Typography>
                  </Box>
                  <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                    💡 If PDF parsing fails, try converting your resume to .txt format
                  </Typography>
                </Box>

                {/* Resume content editor */}
                {resumeText && (
                  <TextField
                    multiline
                    rows={6}
                    fullWidth
                    value={resumeText}
                    onChange={(e) => setResumeText(e.target.value)}
                    placeholder="Your resume content will appear here..."
                    variant="outlined"
                    label="Resume Content (editable)"
                  />
                )}
              </Box>

              {/* Job Description Text Area */}
              <Box>
                <Typography variant="h6" gutterBottom>
                  Job Description {jobDescriptionText && <Chip label="Added" color="success" size="small" sx={{ ml: 1 }} />}
                </Typography>
                <TextField
                  multiline
                  rows={6}
                  fullWidth
                  value={jobDescriptionText}
                  onChange={(e) => setJobDescriptionText(e.target.value)}
                  placeholder="Paste the job description here or type it manually..."
                  variant="outlined"
                  label="Job Description (paste or type)"
                />
                <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                  Copy and paste the job description from the job posting to get targeted interview coaching.
                </Typography>
              </Box>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={clearDocuments} color="error">
              Clear All
            </Button>
            {(resumeText || jobDescriptionText) && (
              <Button
                onClick={() => setShowPromptPreview(true)}
                color="info"
              >
                Preview AI Context
              </Button>
            )}
            <Button onClick={() => setShowDocumentUpload(false)}>
              Cancel
            </Button>
            <Button
              onClick={() => setShowDocumentUpload(false)}
              variant="contained"
              disabled={!resumeText && !jobDescriptionText}
            >
              Save & Use Context
            </Button>
          </DialogActions>
        </Dialog>

        {/* Prompt Preview Dialog */}
        <Dialog
          open={showPromptPreview}
          onClose={() => setShowPromptPreview(false)}
          maxWidth="lg"
          fullWidth
        >
          <DialogTitle>
            AI Context Preview
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              This is how your resume and job description are provided to the AI
            </Typography>
          </DialogTitle>
          <DialogContent>
            <TextField
              multiline
              rows={15}
              fullWidth
              value={getEnhancedSystemPrompt(appConfig.gptSystemPrompt)}
              variant="outlined"
              InputProps={{
                readOnly: true,
                style: { fontSize: '0.875rem', fontFamily: 'monospace' }
              }}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowPromptPreview(false)}>
              Close
            </Button>
          </DialogActions>
        </Dialog>

        <Snackbar
          open={snackbarOpen}
          autoHideDuration={4000}
          onClose={handleSnackbarClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        >
          <Alert onClose={handleSnackbarClose} severity={snackbarSeverity} sx={{ width: '100%', boxShadow: theme.shadows[6] }}>
            {snackbarMessage}
          </Alert>
        </Snackbar>
      </Box>
      <style jsx global>{`
        .scroll-to-bottom {
          height: 100%;
          width: 100%;
          overflow-y: auto;
        }
        .hidden-follow-button {
          display: none;
        }
        .scroll-to-bottom::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }
        .scroll-to-bottom::-webkit-scrollbar-track {
          background: ${theme.palette.background.paper};
          border-radius: 10px;
        }
        .scroll-to-bottom::-webkit-scrollbar-thumb {
          background-color: ${theme.palette.grey[400]};
          border-radius: 10px;
          border: 2px solid ${theme.palette.background.paper};
        }
        .scroll-to-bottom::-webkit-scrollbar-thumb:hover {
          background-color: ${theme.palette.grey[500]};
        }
        .scroll-to-bottom {
          scrollbar-width: thin;
          scrollbar-color: ${theme.palette.grey[400]} ${theme.palette.background.paper};
        }
      `}</style>
    </>
  );
}