import formidable from 'formidable';
import fs from 'fs';
import path from 'path';

export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const form = formidable({
      uploadDir: path.join(process.cwd(), 'public', 'resumes'),
      keepExtensions: true,
      maxFileSize: 10 * 1024 * 1024, // 10MB limit
    });

    // Ensure upload directory exists
    const uploadDir = path.join(process.cwd(), 'public', 'resumes');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    const [fields, files] = await form.parse(req);
    
    if (!files.resume || !files.resume[0]) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const file = files.resume[0];
    const originalName = file.originalFilename || 'uploaded_resume';
    const fileExtension = path.extname(originalName);
    const timestamp = Date.now();
    const newFileName = `uploaded_resume_${timestamp}${fileExtension}`;
    const newFilePath = path.join(uploadDir, newFileName);

    // Move file to final location
    fs.renameSync(file.filepath, newFilePath);

    console.log(`Resume uploaded successfully: ${newFileName}`);

    res.status(200).json({
      success: true,
      fileName: newFileName,
      filePath: `/resumes/${newFileName}`,
      message: 'Resume uploaded successfully'
    });

  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({ 
      error: 'Upload failed', 
      details: error.message 
    });
  }
}
