{"name": "latest", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@deepgram/sdk": "^3.8.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.4.1", "@mui/material": "^6.4.1", "@reduxjs/toolkit": "^2.5.1", "highlight.js": "^11.11.1", "html2canvas": "^1.4.1", "lodash.throttle": "^4.1.1", "microsoft-cognitiveservices-speech-sdk": "^1.45.0", "next": "^15.4.6", "openai": "^4.104.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^9.0.3", "react-redux": "^9.2.0", "react-scroll-to-bottom": "^4.2.0", "react-syntax-highlighter": "^15.6.1"}}