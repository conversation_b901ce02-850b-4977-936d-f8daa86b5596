# Use the official Node.js 18 Alpine image as base
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install dumb-init for proper signal handling
RUN apk add --no-cache dumb-init

# Copy package files first for better layer caching
COPY package*.json ./

# Install dependencies (use ci for production builds)
RUN npm ci --only=production && npm cache clean --force

# Create a non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# Copy the rest of the application code
COPY . .

# Set build-time environment variables for Next.js
ARG NEXT_PUBLIC_DEEPGRAM_API_KEY
ARG NEXT_PUBLIC_AZURE_SPEECH_KEY
ARG NEXT_PUBLIC_AZURE_SPEECH_REGION
ARG NEXT_PUBLIC_OPENAI_API_KEY
ARG NEXT_PUBLIC_GEMINI_API_KEY

ENV NEXT_PUBLIC_DEEPGRAM_API_KEY=$NEXT_PUBLIC_DEEPGRAM_API_KEY
ENV NEXT_PUBLIC_AZURE_SPEECH_KEY=$NEXT_PUBLIC_AZURE_SPEECH_KEY
ENV NEXT_PUBLIC_AZURE_SPEECH_REGION=$NEXT_PUBLIC_AZURE_SPEECH_REGION
ENV NEXT_PUBLIC_OPENAI_API_KEY=$NEXT_PUBLIC_OPENAI_API_KEY
ENV NEXT_PUBLIC_GEMINI_API_KEY=$NEXT_PUBLIC_GEMINI_API_KEY

# Build the Next.js application with environment variables
RUN npm run build

# Change ownership of the app directory to the nextjs user
RUN chown -R nextjs:nodejs /app

# Switch to non-root user
USER nextjs

# Expose the port the app runs on
EXPOSE 3000

# Set environment to production
ENV NODE_ENV=production

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start the application
CMD ["npm", "start"]
